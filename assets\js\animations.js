// ===== Advanced Animation Controller =====
class AnimationController {
    constructor() {
        this.observers = new Map();
        this.animationQueue = [];
        this.isAnimating = false;
        this.init();
    }
    
    init() {
        this.setupIntersectionObserver();
        this.setupScrollAnimations();
        this.setupHoverEffects();
        this.setupParallaxEffects();
        this.setupCounterAnimations();
    }
    
    // ===== Intersection Observer for Scroll Animations =====
    setupIntersectionObserver() {
        const options = {
            threshold: [0.1, 0.3, 0.5, 0.7],
            rootMargin: '0px 0px -100px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.triggerAnimation(entry.target, entry.intersectionRatio);
                }
            });
        }, options);
        
        // Observe all animatable elements
        const animatableElements = document.querySelectorAll(
            '.about-card, .skill-category, .project-card, .contact-card, .hero-card'
        );
        
        animatableElements.forEach(el => {
            observer.observe(el);
        });
        
        this.observers.set('scroll', observer);
    }
    
    // ===== Trigger Animation Based on Element Type =====
    triggerAnimation(element, ratio) {
        if (element.classList.contains('animated')) return;
        
        element.classList.add('animated');
        
        if (element.classList.contains('about-card')) {
            this.animateAboutCard(element);
        } else if (element.classList.contains('skill-category')) {
            this.animateSkillCategory(element);
        } else if (element.classList.contains('project-card')) {
            this.animateProjectCard(element);
        } else if (element.classList.contains('contact-card')) {
            this.animateContactCard(element);
        } else if (element.classList.contains('hero-card')) {
            this.animateHeroCard(element);
        }
    }
    
    // ===== Specific Animation Methods =====
    animateAboutCard(element) {
        element.style.opacity = '0';
        element.style.transform = 'translateY(50px) scale(0.9)';
        
        setTimeout(() => {
            element.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0) scale(1)';
        }, 100);
        
        // Animate icon
        const icon = element.querySelector('.about-icon');
        if (icon) {
            setTimeout(() => {
                icon.style.animation = 'pulse 2s ease-in-out infinite';
            }, 500);
        }
    }
    
    animateSkillCategory(element) {
        const skillItems = element.querySelectorAll('.skill-item');
        
        element.style.opacity = '0';
        element.style.transform = 'translateX(-50px)';
        
        setTimeout(() => {
            element.style.transition = 'all 0.6s ease-out';
            element.style.opacity = '1';
            element.style.transform = 'translateX(0)';
        }, 100);
        
        // Animate skill items with stagger
        skillItems.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'scale(0)';
            
            setTimeout(() => {
                item.style.transition = 'all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
                item.style.opacity = '1';
                item.style.transform = 'scale(1)';
            }, 300 + (index * 100));
        });
    }
    
    animateProjectCard(element) {
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px) rotateX(15deg)';
        
        setTimeout(() => {
            element.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0) rotateX(0deg)';
        }, 100);
        
        // Animate tech tags
        const techTags = element.querySelectorAll('.tech-tag');
        techTags.forEach((tag, index) => {
            tag.style.opacity = '0';
            tag.style.transform = 'translateX(-20px)';
            
            setTimeout(() => {
                tag.style.transition = 'all 0.3s ease-out';
                tag.style.opacity = '1';
                tag.style.transform = 'translateX(0)';
            }, 500 + (index * 100));
        });
    }
    
    animateContactCard(element) {
        element.style.opacity = '0';
        element.style.transform = 'scale(0.8) rotate(5deg)';
        
        setTimeout(() => {
            element.style.transition = 'all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
            element.style.opacity = '1';
            element.style.transform = 'scale(1) rotate(0deg)';
        }, 100);
    }
    
    animateHeroCard(element) {
        const title = element.querySelector('.hero-title');
        const subtitle = element.querySelector('.hero-subtitle');
        const buttons = element.querySelectorAll('.btn');
        
        // Animate title
        if (title) {
            title.style.opacity = '0';
            title.style.transform = 'translateY(30px)';
            setTimeout(() => {
                title.style.transition = 'all 1s ease-out';
                title.style.opacity = '1';
                title.style.transform = 'translateY(0)';
            }, 300);
        }
        
        // Animate subtitle
        if (subtitle) {
            subtitle.style.opacity = '0';
            subtitle.style.transform = 'translateY(20px)';
            setTimeout(() => {
                subtitle.style.transition = 'all 0.8s ease-out';
                subtitle.style.opacity = '1';
                subtitle.style.transform = 'translateY(0)';
            }, 600);
        }
        
        // Animate buttons
        buttons.forEach((btn, index) => {
            btn.style.opacity = '0';
            btn.style.transform = 'translateY(20px) scale(0.9)';
            setTimeout(() => {
                btn.style.transition = 'all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
                btn.style.opacity = '1';
                btn.style.transform = 'translateY(0) scale(1)';
            }, 900 + (index * 200));
        });
    }
    
    // ===== Scroll-based Animations =====
    setupScrollAnimations() {
        let ticking = false;
        
        const updateScrollAnimations = () => {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            
            // Parallax effect for floating shapes
            const shapes = document.querySelectorAll('.shape');
            shapes.forEach((shape, index) => {
                const speed = 0.5 + (index * 0.1);
                shape.style.transform = `translateY(${scrolled * speed}px)`;
            });
            
            // Update navbar opacity based on scroll
            const navbar = document.querySelector('.navbar');
            if (navbar) {
                const opacity = Math.min(scrolled / 100, 1);
                navbar.style.background = `rgba(255, 255, 255, ${0.1 + (opacity * 0.1)})`;
            }
            
            ticking = false;
        };
        
        const requestScrollUpdate = () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollAnimations);
                ticking = true;
            }
        };
        
        window.addEventListener('scroll', requestScrollUpdate);
    }
    
    // ===== Hover Effects =====
    setupHoverEffects() {
        // Enhanced card hover effects
        const cards = document.querySelectorAll('.glass-effect');
        
        cards.forEach(card => {
            card.addEventListener('mouseenter', (e) => {
                this.addHoverEffect(e.target);
            });
            
            card.addEventListener('mouseleave', (e) => {
                this.removeHoverEffect(e.target);
            });
            
            card.addEventListener('mousemove', (e) => {
                this.updateHoverEffect(e);
            });
        });
    }
    
    addHoverEffect(element) {
        element.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        element.style.transform = 'translateY(-10px) scale(1.02)';
        element.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.2)';
    }
    
    removeHoverEffect(element) {
        element.style.transform = 'translateY(0) scale(1)';
        element.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.1)';
    }
    
    updateHoverEffect(e) {
        const card = e.currentTarget;
        const rect = card.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;
        
        const rotateX = (y - centerY) / 10;
        const rotateY = (centerX - x) / 10;
        
        card.style.transform = `translateY(-10px) scale(1.02) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
    }
    
    // ===== Parallax Effects =====
    setupParallaxEffects() {
        const parallaxElements = document.querySelectorAll('[data-parallax]');
        
        const updateParallax = () => {
            const scrolled = window.pageYOffset;
            
            parallaxElements.forEach(element => {
                const speed = element.dataset.parallax || 0.5;
                const yPos = -(scrolled * speed);
                element.style.transform = `translateY(${yPos}px)`;
            });
        };
        
        window.addEventListener('scroll', () => {
            requestAnimationFrame(updateParallax);
        });
    }
    
    // ===== Counter Animations =====
    setupCounterAnimations() {
        const counters = document.querySelectorAll('[data-counter]');
        
        const animateCounter = (element) => {
            const target = parseInt(element.dataset.counter);
            const duration = 2000;
            const step = target / (duration / 16);
            let current = 0;
            
            const updateCounter = () => {
                current += step;
                if (current < target) {
                    element.textContent = Math.floor(current);
                    requestAnimationFrame(updateCounter);
                } else {
                    element.textContent = target;
                }
            };
            
            updateCounter();
        };
        
        const counterObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !entry.target.classList.contains('counted')) {
                    entry.target.classList.add('counted');
                    animateCounter(entry.target);
                }
            });
        });
        
        counters.forEach(counter => {
            counterObserver.observe(counter);
        });
    }
    
    // ===== Utility Methods =====
    addToQueue(animation) {
        this.animationQueue.push(animation);
        if (!this.isAnimating) {
            this.processQueue();
        }
    }
    
    processQueue() {
        if (this.animationQueue.length === 0) {
            this.isAnimating = false;
            return;
        }
        
        this.isAnimating = true;
        const animation = this.animationQueue.shift();
        animation().then(() => {
            this.processQueue();
        });
    }
    
    // ===== Public API =====
    animateElement(element, animation, delay = 0) {
        setTimeout(() => {
            element.style.animation = animation;
        }, delay);
    }
    
    resetAnimation(element) {
        element.style.animation = 'none';
        element.offsetHeight; // Trigger reflow
        element.classList.remove('animated');
    }
}

// ===== Initialize Animation Controller =====
document.addEventListener('DOMContentLoaded', () => {
    window.animationController = new AnimationController();
});

// ===== Custom Animation Utilities =====
const AnimationUtils = {
    // Easing functions
    easing: {
        easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
        easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
        easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
        bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
    },
    
    // Animation presets
    fadeIn: (element, duration = 0.5, delay = 0) => {
        element.style.opacity = '0';
        element.style.transition = `opacity ${duration}s ${AnimationUtils.easing.easeOut} ${delay}s`;
        
        setTimeout(() => {
            element.style.opacity = '1';
        }, 10);
    },
    
    slideIn: (element, direction = 'up', duration = 0.6, delay = 0) => {
        const transforms = {
            up: 'translateY(30px)',
            down: 'translateY(-30px)',
            left: 'translateX(30px)',
            right: 'translateX(-30px)'
        };
        
        element.style.opacity = '0';
        element.style.transform = transforms[direction];
        element.style.transition = `all ${duration}s ${AnimationUtils.easing.easeOut} ${delay}s`;
        
        setTimeout(() => {
            element.style.opacity = '1';
            element.style.transform = 'translate(0, 0)';
        }, 10);
    },
    
    scaleIn: (element, duration = 0.5, delay = 0) => {
        element.style.opacity = '0';
        element.style.transform = 'scale(0.8)';
        element.style.transition = `all ${duration}s ${AnimationUtils.easing.bounce} ${delay}s`;
        
        setTimeout(() => {
            element.style.opacity = '1';
            element.style.transform = 'scale(1)';
        }, 10);
    }
};

// ===== Export for global use =====
window.AnimationUtils = AnimationUtils;
