<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人主页 - Portfolio</title>
    <meta name="description" content="现代化个人主页，展示技能与项目">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/animations.css">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar glass-effect">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-code"></i>
                <span>Portfolio</span>
            </div>
            <ul class="nav-menu">
                <li><a href="#home" class="nav-link"><i class="fas fa-home"></i> 首页</a></li>
                <li><a href="#about" class="nav-link"><i class="fas fa-user"></i> 关于</a></li>
                <li><a href="#skills" class="nav-link"><i class="fas fa-cogs"></i> 技能</a></li>
                <li><a href="#projects" class="nav-link"><i class="fas fa-folder-open"></i> 项目</a></li>
                <li><a href="#contact" class="nav-link"><i class="fas fa-envelope"></i> 联系</a></li>
            </ul>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-background">
            <div class="floating-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
                <div class="shape shape-4"></div>
            </div>
        </div>
        <div class="hero-content">
            <div class="hero-card glass-effect">
                <h1 class="hero-title">
                    <span class="typing-text">欢迎来到我的数字世界</span>
                </h1>
                <p class="hero-subtitle">创新 · 设计 · 开发</p>
                <div class="hero-buttons">
                    <a href="#projects" class="btn btn-primary">
                        <i class="fas fa-rocket"></i>
                        查看作品
                    </a>
                    <a href="#contact" class="btn btn-secondary">
                        <i class="fas fa-paper-plane"></i>
                        联系我
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">关于我</h2>
                <p class="section-subtitle">专注于创造优秀的数字体验</p>
            </div>
            <div class="about-content">
                <div class="about-card glass-effect">
                    <div class="about-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <h3>创新思维</h3>
                    <p>始终保持对新技术的好奇心，追求创新的解决方案</p>
                </div>
                <div class="about-card glass-effect">
                    <div class="about-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3>设计美学</h3>
                    <p>注重用户体验，追求简洁优雅的设计风格</p>
                </div>
                <div class="about-card glass-effect">
                    <div class="about-icon">
                        <i class="fas fa-code-branch"></i>
                    </div>
                    <h3>技术实现</h3>
                    <p>精通多种编程语言，能够将创意转化为现实</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="skills">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">技能专长</h2>
                <p class="section-subtitle">掌握的技术栈与工具</p>
            </div>
            <div class="skills-grid">
                <div class="skill-category glass-effect">
                    <div class="skill-header">
                        <i class="fas fa-laptop-code"></i>
                        <h3>前端开发</h3>
                    </div>
                    <div class="skill-items">
                        <div class="skill-item">
                            <i class="fab fa-html5"></i>
                            <span>HTML5</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-css3-alt"></i>
                            <span>CSS3</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-js-square"></i>
                            <span>JavaScript</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-react"></i>
                            <span>React</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-vue"></i>
                            <span>Vue.js</span>
                        </div>
                    </div>
                </div>
                
                <div class="skill-category glass-effect">
                    <div class="skill-header">
                        <i class="fas fa-server"></i>
                        <h3>后端开发</h3>
                    </div>
                    <div class="skill-items">
                        <div class="skill-item">
                            <i class="fab fa-php"></i>
                            <span>PHP</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-node-js"></i>
                            <span>Node.js</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-python"></i>
                            <span>Python</span>
                        </div>
                        <div class="skill-item">
                            <i class="fas fa-database"></i>
                            <span>MySQL</span>
                        </div>
                        <div class="skill-item">
                            <i class="fas fa-cloud"></i>
                            <span>云服务</span>
                        </div>
                    </div>
                </div>
                
                <div class="skill-category glass-effect">
                    <div class="skill-header">
                        <i class="fas fa-tools"></i>
                        <h3>开发工具</h3>
                    </div>
                    <div class="skill-items">
                        <div class="skill-item">
                            <i class="fab fa-git-alt"></i>
                            <span>Git</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-docker"></i>
                            <span>Docker</span>
                        </div>
                        <div class="skill-item">
                            <i class="fas fa-terminal"></i>
                            <span>Terminal</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-figma"></i>
                            <span>Figma</span>
                        </div>
                        <div class="skill-item">
                            <i class="fas fa-mobile-alt"></i>
                            <span>响应式设计</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="projects">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">精选项目</h2>
                <p class="section-subtitle">展示我的最佳作品</p>
            </div>
            <div class="projects-grid">
                <div class="project-card glass-effect">
                    <div class="project-image">
                        <div class="project-overlay">
                            <div class="project-links">
                                <a href="#" class="project-link">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="#" class="project-link">
                                    <i class="fab fa-github"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="project-content">
                        <h3>电商平台</h3>
                        <p>现代化的电商解决方案，具有完整的购物车、支付和用户管理功能</p>
                        <div class="project-tech">
                            <span class="tech-tag">React</span>
                            <span class="tech-tag">Node.js</span>
                            <span class="tech-tag">MongoDB</span>
                        </div>
                    </div>
                </div>
                
                <div class="project-card glass-effect">
                    <div class="project-image">
                        <div class="project-overlay">
                            <div class="project-links">
                                <a href="#" class="project-link">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="#" class="project-link">
                                    <i class="fab fa-github"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="project-content">
                        <h3>任务管理系统</h3>
                        <p>团队协作工具，支持项目管理、任务分配和进度跟踪</p>
                        <div class="project-tech">
                            <span class="tech-tag">Vue.js</span>
                            <span class="tech-tag">PHP</span>
                            <span class="tech-tag">MySQL</span>
                        </div>
                    </div>
                </div>
                
                <div class="project-card glass-effect">
                    <div class="project-image">
                        <div class="project-overlay">
                            <div class="project-links">
                                <a href="#" class="project-link">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="#" class="project-link">
                                    <i class="fab fa-github"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="project-content">
                        <h3>数据可视化平台</h3>
                        <p>交互式数据分析工具，支持多种图表类型和实时数据更新</p>
                        <div class="project-tech">
                            <span class="tech-tag">D3.js</span>
                            <span class="tech-tag">Python</span>
                            <span class="tech-tag">Flask</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">联系方式</h2>
                <p class="section-subtitle">让我们开始合作</p>
            </div>
            <div class="contact-content">
                <div class="contact-info">
                    <div class="contact-card glass-effect">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h3>邮箱</h3>
                        <p><EMAIL></p>
                    </div>
                    <div class="contact-card glass-effect">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <h3>电话</h3>
                        <p>+86 138 0000 0000</p>
                    </div>
                    <div class="contact-card glass-effect">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <h3>位置</h3>
                        <p>中国 · 北京</p>
                    </div>
                </div>
                <div class="contact-form-container">
                    <form class="contact-form glass-effect">
                        <div class="form-group">
                            <input type="text" id="name" name="name" required>
                            <label for="name">姓名</label>
                        </div>
                        <div class="form-group">
                            <input type="email" id="email" name="email" required>
                            <label for="email">邮箱</label>
                        </div>
                        <div class="form-group">
                            <textarea id="message" name="message" required></textarea>
                            <label for="message">消息</label>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i>
                            发送消息
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer glass-effect">
        <div class="container">
            <div class="footer-content">
                <div class="footer-left">
                    <p>&copy; 2024 Portfolio. 保留所有权利。</p>
                </div>
                <div class="footer-right">
                    <div class="social-links">
                        <a href="#" class="social-link">
                            <i class="fab fa-github"></i>
                        </a>
                        <a href="#" class="social-link">
                            <i class="fab fa-linkedin"></i>
                        </a>
                        <a href="#" class="social-link">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-link">
                            <i class="fas fa-rss"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript Files -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/animations.js"></script>
</body>
</html>
