/* ===== CSS Reset & Base Styles ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* ===== Glass Effect ===== */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* ===== Container ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* ===== Navigation ===== */
.navbar {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    width: 90%;
    max-width: 1000px;
    padding: 15px 30px;
    transition: all 0.3s ease;
    /* 确保导航栏始终居中 */
    margin: 0;
    box-sizing: border-box;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
}

.nav-logo i {
    color: #64ffda;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    color: white;
    text-decoration: none;
    font-weight: 500;
    padding: 10px 15px;
    border-radius: 10px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: white;
    border-radius: 2px;
    transition: all 0.3s ease;
}

/* ===== Hero Section ===== */
.hero {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 30%;
    right: 30%;
    animation-delay: 1s;
}

.hero-content {
    text-align: center;
    z-index: 1;
}

.hero-card {
    padding: 60px 40px;
    max-width: 600px;
    margin: 0 auto;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.5rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 40px;
    font-weight: 300;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

/* ===== Buttons ===== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(45deg, #64ffda, #00bcd4);
    color: #1a1a1a;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(100, 255, 218, 0.3);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
}

/* ===== Sections ===== */
section {
    padding: 100px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 80px;
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    color: white;
    margin-bottom: 20px;
}

.section-subtitle {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.7);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== About Section ===== */
.about-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.about-card {
    padding: 40px 30px;
    text-align: center;
    transition: all 0.3s ease;
}

.about-card:hover {
    transform: translateY(-10px);
}

.about-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    background: linear-gradient(45deg, #64ffda, #00bcd4);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: #1a1a1a;
}

.about-card h3 {
    font-size: 1.5rem;
    color: white;
    margin-bottom: 15px;
}

.about-card p {
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.6;
}

/* ===== Skills Section ===== */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.skill-category {
    padding: 40px 30px;
    transition: all 0.3s ease;
}

.skill-category:hover {
    transform: translateY(-5px);
}

.skill-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 30px;
}

.skill-header i {
    font-size: 2rem;
    color: #64ffda;
}

.skill-header h3 {
    font-size: 1.5rem;
    color: white;
}

.skill-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.skill-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 20px 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    transition: all 0.3s ease;
}

.skill-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.05);
}

.skill-item i {
    font-size: 2rem;
    color: #64ffda;
}

.skill-item span {
    color: white;
    font-size: 0.9rem;
    font-weight: 500;
}

/* ===== Projects Section ===== */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.project-card {
    overflow: hidden;
    transition: all 0.3s ease;
}

.project-card:hover {
    transform: translateY(-10px);
}

.project-image {
    height: 200px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    position: relative;
    overflow: hidden;
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-links {
    display: flex;
    gap: 15px;
}

.project-link {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.project-link:hover {
    background: #64ffda;
    color: #1a1a1a;
    transform: scale(1.1);
}

.project-content {
    padding: 30px;
}

.project-content h3 {
    font-size: 1.5rem;
    color: white;
    margin-bottom: 15px;
}

.project-content p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 20px;
    line-height: 1.6;
}

.project-tech {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.tech-tag {
    padding: 5px 15px;
    background: rgba(100, 255, 218, 0.2);
    color: #64ffda;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* ===== Contact Section ===== */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.contact-card {
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
}

.contact-card:hover {
    transform: translateY(-5px);
}

.contact-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 20px;
    background: linear-gradient(45deg, #64ffda, #00bcd4);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #1a1a1a;
}

.contact-card h3 {
    font-size: 1.2rem;
    color: white;
    margin-bottom: 10px;
}

.contact-card p {
    color: rgba(255, 255, 255, 0.7);
}

/* ===== Contact Form ===== */
.contact-form {
    padding: 40px;
}

.form-group {
    position: relative;
    margin-bottom: 30px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #64ffda;
    background: rgba(255, 255, 255, 0.15);
}

.form-group label {
    position: absolute;
    top: 15px;
    left: 20px;
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;
    pointer-events: none;
}

.form-group input:focus + label,
.form-group input:valid + label,
.form-group textarea:focus + label,
.form-group textarea:valid + label {
    top: -10px;
    left: 15px;
    font-size: 0.8rem;
    color: #64ffda;
    background: rgba(102, 126, 234, 0.8);
    padding: 2px 8px;
    border-radius: 5px;
}

.form-group textarea {
    height: 120px;
    resize: vertical;
}

/* ===== Footer ===== */
.footer {
    margin-top: 50px;
    padding: 30px 0;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-left p {
    color: rgba(255, 255, 255, 0.7);
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: #64ffda;
    color: #1a1a1a;
    transform: translateY(-3px);
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .navbar {
        /* 确保移动端导航栏也正确居中 */
        position: fixed !important;
        top: 20px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        width: 95% !important;
        max-width: none !important;
        padding: 12px 20px !important;
    }

    .nav-menu {
        position: fixed;
        top: 100px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 100px);
        background: rgba(0, 0, 0, 0.9);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 50px;
        transition: all 0.3s ease;
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .skills-grid {
        grid-template-columns: 1fr;
    }
    
    .projects-grid {
        grid-template-columns: 1fr;
    }
}

/* ===== 平板设备适配 ===== */
@media (max-width: 1024px) and (min-width: 769px) {
    .navbar {
        width: 92%;
        padding: 14px 25px;
    }
}

/* ===== 大屏幕设备确保居中 ===== */
@media (min-width: 1025px) {
    .navbar {
        /* 确保大屏幕下导航栏正确居中 */
        position: fixed !important;
        top: 20px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
    }
}

/* ===== 导航栏强制居中样式 ===== */
.navbar-centered {
    position: fixed !important;
    top: 20px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 1000 !important;
}
