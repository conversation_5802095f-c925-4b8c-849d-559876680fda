# 个人主页网站 - Apple Glass Morphism Design

一个采用苹果毛玻璃UI设计风格的现代化个人主页网站，使用PHP、HTML、CSS和JavaScript构建。

## 🎨 设计特色

- **苹果毛玻璃效果**: 半透明背景、模糊效果、柔和阴影
- **现代简约**: 简洁专业的界面设计
- **响应式布局**: 完美适配桌面和移动设备
- **流畅动画**: 丰富的交互动画和过渡效果
- **专业图标**: 使用Font Awesome图标库

## 📁 项目结构

```
个人主页/
├── index.php                 # 主页文件
├── assets/                   # 资源文件夹
│   ├── css/                 # 样式文件
│   │   ├── style.css        # 主要样式
│   │   └── animations.css   # 动画样式
│   └── js/                  # JavaScript文件
│       ├── main.js          # 主要功能
│       └── animations.js    # 动画控制
└── README.md                # 项目说明
```

## 🚀 功能特性

### 核心功能
- **响应式导航栏**: 固定顶部，毛玻璃效果
- **英雄区域**: 动态打字效果，浮动动画
- **关于部分**: 三栏式介绍，图标动画
- **技能展示**: 分类技能展示，交互动画
- **项目展示**: 项目卡片，悬停效果
- **联系表单**: 表单验证，提交反馈
- **社交链接**: 底部社交媒体链接

### 动画效果
- **滚动动画**: 元素进入视窗时触发
- **悬停效果**: 卡片3D变换效果
- **打字动画**: 首页标题动态打字
- **粒子系统**: 背景浮动粒子
- **进度指示**: 页面滚动进度条
- **涟漪效果**: 按钮点击涟漪动画

### 交互功能
- **平滑滚动**: 锚点平滑跳转
- **移动菜单**: 响应式汉堡菜单
- **表单验证**: 实时表单验证
- **通知系统**: 操作反馈通知
- **视差滚动**: 背景视差效果

## 🛠️ 技术栈

- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **样式**: CSS Grid, Flexbox, CSS Variables
- **动画**: CSS Animations, Intersection Observer API
- **图标**: Font Awesome 6.4.0
- **字体**: Google Fonts (Inter)
- **兼容性**: 现代浏览器支持

## 📱 响应式设计

### 断点设置
- **桌面**: > 768px
- **平板**: 768px - 1024px  
- **手机**: < 768px

### 适配特性
- 流体布局适配不同屏幕尺寸
- 移动端优化的导航菜单
- 触摸友好的交互元素
- 优化的字体大小和间距

## 🎯 使用方法

### 1. 环境要求
- PHP 7.0+ (用于服务器运行)
- 现代浏览器 (Chrome, Firefox, Safari, Edge)
- 网络连接 (加载外部字体和图标)

### 2. 安装部署
```bash
# 克隆或下载项目文件
# 将文件放置到Web服务器目录
# 访问 index.php
```

### 3. 自定义配置

#### 修改个人信息
编辑 `index.php` 文件中的以下部分：
- 联系信息 (邮箱、电话、地址)
- 项目展示内容
- 技能列表
- 社交媒体链接

#### 样式自定义
编辑 `assets/css/style.css`:
- 修改颜色主题
- 调整布局间距
- 更改字体设置

#### 功能扩展
编辑 `assets/js/main.js`:
- 添加新的交互功能
- 修改动画效果
- 集成第三方服务

## 🎨 颜色方案

```css
/* 主要颜色 */
--primary-color: #64ffda;      /* 青色强调 */
--secondary-color: #00bcd4;    /* 蓝绿色 */
--background-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

/* 玻璃效果 */
--glass-background: rgba(255, 255, 255, 0.1);
--glass-border: rgba(255, 255, 255, 0.2);
--glass-shadow: rgba(0, 0, 0, 0.1);
```

## 📊 性能优化

- **CSS优化**: 使用CSS变量，避免重复代码
- **JavaScript优化**: 防抖和节流函数优化滚动事件
- **图片优化**: 使用矢量图标，减少HTTP请求
- **动画优化**: 使用transform和opacity进行硬件加速
- **代码分离**: CSS和JS文件分离，便于缓存

## 🔧 浏览器兼容性

| 浏览器 | 版本要求 | 支持状态 |
|--------|----------|----------|
| Chrome | 60+ | ✅ 完全支持 |
| Firefox | 55+ | ✅ 完全支持 |
| Safari | 12+ | ✅ 完全支持 |
| Edge | 79+ | ✅ 完全支持 |
| IE | 不支持 | ❌ 不兼容 |

## 📝 自定义指南

### 1. 修改配色方案
```css
/* 在 style.css 中修改 */
:root {
    --primary-color: #your-color;
    --secondary-color: #your-color;
}
```

### 2. 添加新的技能项
```html
<!-- 在 index.php 的技能部分添加 -->
<div class="skill-item">
    <i class="fab fa-your-icon"></i>
    <span>技能名称</span>
</div>
```

### 3. 添加新的项目
```html
<!-- 在项目展示部分添加 -->
<div class="project-card glass-effect">
    <!-- 项目内容 -->
</div>
```

## 🚀 部署建议

### 开发环境
- 使用本地PHP服务器: `php -S localhost:8000`
- 或使用XAMPP/WAMP等集成环境

### 生产环境
- 上传到支持PHP的Web主机
- 确保服务器支持现代PHP版本
- 配置适当的缓存策略

## 📄 许可证

本项目采用 MIT 许可证 - 详见 LICENSE 文件

## 🤝 贡献

欢迎提交问题和功能请求！

## 📞 支持

如有问题，请通过以下方式联系：
- 邮箱: <EMAIL>
- 项目Issues页面

---

**注意**: 这是一个前端展示项目，不包含后端数据处理功能。如需添加真实的表单提交功能，请集成相应的后端服务。
