/* ===== Keyframe Animations ===== */

/* Floating Animation */
@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-20px) rotate(120deg);
    }
    66% {
        transform: translateY(10px) rotate(240deg);
    }
}

/* Fade In Up Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Fade In Left Animation */
@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Fade In Right Animation */
@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Scale In Animation */
@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Pulse Animation */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Typing Animation */
@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink {
    0%, 50% {
        border-color: transparent;
    }
    51%, 100% {
        border-color: #64ffda;
    }
}

/* Gradient Animation */
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Bounce Animation */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

/* Slide In Animation */
@keyframes slideInDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Rotate Animation */
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Glow Animation */
@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(100, 255, 218, 0.3);
    }
    50% {
        box-shadow: 0 0 40px rgba(100, 255, 218, 0.6);
    }
}

/* ===== Animation Classes ===== */

/* Fade Animations */
.fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
}

.fade-in-left {
    animation: fadeInLeft 0.8s ease-out forwards;
}

.fade-in-right {
    animation: fadeInRight 0.8s ease-out forwards;
}

.scale-in {
    animation: scaleIn 0.6s ease-out forwards;
}

/* Delay Classes */
.delay-1 {
    animation-delay: 0.1s;
}

.delay-2 {
    animation-delay: 0.2s;
}

.delay-3 {
    animation-delay: 0.3s;
}

.delay-4 {
    animation-delay: 0.4s;
}

.delay-5 {
    animation-delay: 0.5s;
}

/* Typing Effect */
.typing-text {
    display: inline-block;
    overflow: hidden;
    border-right: 3px solid #64ffda;
    white-space: nowrap;
    animation: typing 3s steps(30, end), blink 1s infinite;
}

/* Hover Animations */
.hover-float:hover {
    animation: float 2s ease-in-out infinite;
}

.hover-pulse:hover {
    animation: pulse 1s ease-in-out infinite;
}

.hover-bounce:hover {
    animation: bounce 1s ease-in-out;
}

.hover-glow:hover {
    animation: glow 2s ease-in-out infinite;
}

/* Background Animations */
.animated-gradient {
    background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

/* Loading Animations */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #64ffda;
    border-radius: 50%;
    animation: rotate 1s linear infinite;
}

/* Scroll Animations */
.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s ease-out;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Stagger Animation */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }
.stagger-6 { animation-delay: 0.6s; }

/* Navbar Animation */
.navbar-slide {
    animation: slideInDown 0.8s ease-out;
}

/* Button Animations */
.btn-animate {
    position: relative;
    overflow: hidden;
}

.btn-animate::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-animate:hover::before {
    left: 100%;
}

/* Card Hover Effects */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

/* Text Animations */
.text-glow {
    text-shadow: 0 0 10px rgba(100, 255, 218, 0.5);
}

.text-gradient {
    background: linear-gradient(45deg, #64ffda, #00bcd4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Particle Animation */
.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(100, 255, 218, 0.6);
    border-radius: 50%;
    animation: float 8s ease-in-out infinite;
}

/* Progress Bar Animation */
@keyframes progressFill {
    from {
        width: 0%;
    }
    to {
        width: var(--progress-width);
    }
}

.progress-bar {
    animation: progressFill 2s ease-out forwards;
}

/* Ripple Effect */
.ripple {
    position: relative;
    overflow: hidden;
}

.ripple::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple:active::after {
    width: 300px;
    height: 300px;
}

/* Smooth Transitions */
.smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Entrance Animations */
.entrance-fade {
    opacity: 0;
    animation: fadeInUp 1s ease-out 0.5s forwards;
}

.entrance-slide {
    transform: translateX(-100px);
    opacity: 0;
    animation: fadeInLeft 1s ease-out 0.3s forwards;
}

.entrance-scale {
    transform: scale(0);
    opacity: 0;
    animation: scaleIn 0.8s ease-out 0.4s forwards;
}
